import { Star } from "lucide-react";
import { Card } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";

interface StudioCardProps {
  id: string;
  name: string;
  location: string;
  rating: number;
  image: string;
}

export const StudioCard = ({ id, name, location, rating, image }: StudioCardProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(`/salon/${id}`);
  };

  return (
    <Card 
      className="group cursor-pointer overflow-hidden border-0 shadow-sm hover:shadow-lg transition-all duration-300"
      onClick={handleClick}
    >
      <div className="aspect-[4/3] overflow-hidden rounded-lg bg-glamspot-neutral-100">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>

      <div className="p-3 sm:p-4 space-y-2">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-glamspot-neutral-900 truncate text-sm sm:text-base">{name}</h3>
            <p className="text-xs sm:text-sm text-glamspot-neutral-500 truncate">
              {location}
            </p>
          </div>

          <div className="flex items-center gap-1 flex-shrink-0">
            <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-current" />
            <span className="text-xs sm:text-sm font-medium text-glamspot-neutral-900">{rating}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};