import { useState } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { SearchBar } from "./SearchBar";
import { Globe, Menu, User, LogOut, Settings, LayoutDashboard } from "lucide-react";

export const Header = () => {
  const { user, isAuthenticated, isAdmin, isSalonOwner, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="sticky top-0 z-50 bg-white border-b border-glamspot-neutral-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 gap-2 sm:gap-4 lg:gap-8">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 text-glamspot-primary hover:text-glamspot-primary-dark transition-colors flex-shrink-0">
            <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center">
              <img src="/logo.svg" alt="GlamSpot" className="w-6 h-6 sm:w-8 sm:h-8" />
            </div>
            <h1 className="text-lg sm:text-2xl font-bold tracking-tight hidden xs:block">GlamSpot</h1>
          </Link>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-2 sm:mx-4">
            <SearchBar />
          </div>

          {/* Right Navigation */}
          <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
            <Button variant="ghost" className="hidden lg:flex text-glamspot-neutral-700 hover:text-glamspot-primary text-sm" asChild>
              <Link to="/register-salon">List your space</Link>
            </Button>

            <Button variant="ghost" size="icon" className="text-glamspot-neutral-700 hover:text-glamspot-primary w-8 h-8 sm:w-10 sm:h-10">
              <Globe className="w-4 h-4 sm:w-5 sm:h-5" />
            </Button>

            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2 border border-glamspot-neutral-200 rounded-full p-2 hover:shadow-md transition-shadow">
                    <Menu className="w-4 h-4 text-glamspot-neutral-700" />
                    <div className="w-8 h-8 bg-glamspot-primary rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {user?.name?.charAt(0) || 'U'}
                      </span>
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium">{user?.name}</p>
                      <p className="text-xs text-glamspot-neutral-500">{user?.email}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  {isAdmin && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center">
                        <LayoutDashboard className="w-4 h-4 mr-2" />
                        Admin Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}

                  {isSalonOwner && (
                    <DropdownMenuItem asChild>
                      <Link to="/salon-owner" className="flex items-center">
                        <Settings className="w-4 h-4 mr-2" />
                        Salon Dashboard
                      </Link>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center gap-2">
                <Button variant="ghost" asChild>
                  <Link to="/login">Sign in</Link>
                </Button>
                <div className="flex items-center gap-2 border border-glamspot-neutral-200 rounded-full p-2 hover:shadow-md transition-shadow">
                  <Menu className="w-4 h-4 text-glamspot-neutral-700" />
                  <div className="w-8 h-8 bg-glamspot-neutral-500 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};